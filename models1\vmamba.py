import torch
import torch.nn as nn
import torch.nn.functional as F
from functools import partial
from mamba_ssm.modules.mamba_simple import Mamba



class MSSBlock(nn.Module):
    """视觉Mamba基本构建块
    
    包含层归一化、Mamba模块和残差连接的基本构建块
    
    参数:
        hidden_dim: 隐藏层维度
        drop_path: 随机路径丢弃率，用于正则化
        norm_layer: 归一化层类型，默认为LayerNorm
        d_state: 状态空间维度
        d_conv: 卷积核大小
        expand: 扩展因子
        **kwargs: 传递给Mamba的额外参数
    """
    def __init__(
        self,
        hidden_dim: int = 0,
        drop_path: float = 0,
        norm_layer: nn.Module = partial(nn.LayerNorm, eps=1e-6),
        d_state: int = 16,
        d_conv: int = 4,
        expand: int = 2,
        **kwargs,
    ):
        super().__init__()  # 调用父类初始化
        self.hidden_dim = hidden_dim
        self.ln_1 = norm_layer(hidden_dim)  # 创建层归一化，用于特征归一化
        
        # 使用Mamba作为序列处理模块
        self.self_attention = Mamba(
            d_model=hidden_dim,  # 直接使用hidden_dim作为Mamba的模型维度
            d_state=d_state,
            d_conv=d_conv,
            expand=expand,
            **kwargs
        )  
        
        # 使用标准的dropout替代DropPath
        self.dropout = nn.Dropout(drop_path) if drop_path > 0 else nn.Identity()  # 创建随机丢弃

    def forward(self, input: torch.Tensor):
        """前向传播函数
        
        参数:
            input: 输入特征，形状为[B, T, C]，B是批量大小，T是时间步，C是通道数
            
        返回:
            处理后的特征，形状为[B, T, C]
        
        应用示例:
            在MSSLayer中：
            block = MSSBlock(hidden_dim=96, d_state=16)
            # 输入特征input形状为[8, 56, 96]
            output = block(input)
            # 1. 首先对input应用层归一化
            # 2. 归一化后的特征通过Mamba模块
            # 3. 应用dropout并与原始input相加(残差连接)
            # output形状仍为[8, 56, 96]，但特征更丰富
            # 这种Pre-Norm结构有助于深层网络的稳定训练
        """
        # 应用层归一化
        x = self.ln_1(input)
        
        # 通过Mamba处理，直接处理[B, T, C]格式输入
        x = self.self_attention(x)
        
        # 应用dropout和残差连接
        x = input + self.dropout(x)
        
        return x  # 返回处理后的特征
